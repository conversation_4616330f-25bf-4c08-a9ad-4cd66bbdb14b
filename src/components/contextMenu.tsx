import React, { useEffect, useState } from 'react';
import { getFormatNumStr } from '../commons/util';
import { usePaneScale } from '../hooks/useScale';
import { ContextMenuContainer, ContextMenuItem, ContextMenuDivider } from '../styles/contextMenuStyles';
import { useAppContext, useChartContext, useDataContext, useNotificationContext } from '../contexts/contexts';
import { createAlertConfig, fetchAlertTriggerTypeList } from '../commons/api';
import { TAlertTriggerType, TAlertOp } from '../types/alertTypes';



export const ContextMenu = (props: { onClose?: () => void }) => {
    const { contextMenuState, symbol, timeframe, selectedDrawingConfig } = useAppContext()
    const { success, error } = useNotificationContext()
    const { chartName, chartStore } = useChartContext()
    contextMenuState.use()
    selectedDrawingConfig.use()
    const coord = contextMenuState.value?.coord
    const { y2v } = usePaneScale()
    const [triggerTypes, setTriggerTypes] = useState<TAlertTriggerType[]>([]);
    const [operations, setOperations] = useState<TAlertOp[]>([]);
    const [selectedAlertType, setSelectedAlertType] = useState<string>('');
    const [selectedTriggerType, setSelectedTriggerType] = useState<string>('');
    const [selectedOperation, setSelectedOperation] = useState<string>('');
    const { alertConfigList } = useDataContext()
    const menuRef = React.useRef<HTMLDivElement>(null);

    useEffect(() => {
        fetchAlertTriggerTypeList().then(setTriggerTypes);
        // Don't fetch operations from API, define them conditionally
    }, []);

    // Calculate menu position using fixed thresholds
    const getMenuPosition = () => {
        if (!coord || !menuRef.current) return { x: coord?.x || 0, y: coord?.y || 0 };

        // Get the pane's position relative to the viewport
        const paneElement = menuRef.current.closest('.konvajs-content') || menuRef.current.closest('[data-testid="chart-pane"]') || menuRef.current.parentElement;
        const paneRect = paneElement?.getBoundingClientRect();

        const screenWidth = window.innerWidth;
        const screenHeight = window.innerHeight;

        // Fixed thresholds for menu size estimation
        const MENU_BASE_WIDTH = 350; // Width when expanded
        const MENU_BASE_HEIGHT = 150; // Base menu height
        const MENU_EXPANDED_HEIGHT = 400; // Height when fully expanded with options

        // Calculate absolute position in viewport
        const paneOffsetX = paneRect?.left || 0;
        const paneOffsetY = paneRect?.top || 0;
        const absoluteX = coord.x + paneOffsetX;
        const absoluteY = coord.y + paneOffsetY;

        let x = coord.x; // Keep relative to pane
        let y = coord.y; // Keep relative to pane

        // Adjust horizontal position if absolute position would go off-screen
        if (absoluteX + MENU_BASE_WIDTH > screenWidth - 20) {
            x = screenWidth - MENU_BASE_WIDTH - 20 - paneOffsetX;
        }
        if (absoluteX < 20) {
            x = 20 - paneOffsetX;
        }

        // Adjust vertical position if absolute position would go off-screen
        const estimatedHeight = selectedAlertType ? MENU_EXPANDED_HEIGHT : MENU_BASE_HEIGHT;
        if (absoluteY + estimatedHeight > screenHeight - 20) {
            y = screenHeight - estimatedHeight - 20 - paneOffsetY;
        }
        if (absoluteY < 20) {
            y = 20 - paneOffsetY;
        }

        return { x, y };
    };

    // Define operations based on selectedAlertType and selectedDrawingConfig
    useEffect(() => {
        let availableOperations: TAlertOp[] = [];

        if (selectedAlertType === 'value') {
            // For value alerts, show cross options
            availableOperations = [
                { name: 'Cross', type: 'cross' },
                { name: 'Cross Up', type: 'crossUp' },
                { name: 'Cross Down', type: 'crossDown' }
            ];
        } else if (selectedAlertType === 'drawing' && selectedDrawingConfig.value?.type === 'horizontalRayChannel') {
            // For horizontal ray channel, show channel options
            availableOperations = [
                { name: 'Cross Channel', type: 'crossChannel' },
                { name: 'Exit Channel', type: 'exitChannel' },
                { name: 'Enter Channel', type: 'enterChannel' }
            ];
        } else if (selectedAlertType === 'drawing' && selectedDrawingConfig.value?.type === 'ray') {
            // For ray drawings, show cross options
            availableOperations = [
                { name: 'Cross', type: 'cross' },
                { name: 'Cross Up', type: 'crossUp' },
                { name: 'Cross Down', type: 'crossDown' }
            ];
        } else {
            // Default fallback for other drawing types
            availableOperations = [
                { name: 'Cross', type: 'cross' },
                { name: 'Cross Up', type: 'crossUp' },
                { name: 'Cross Down', type: 'crossDown' },
                { name: 'Cross Channel', type: 'crossChannel' },
                { name: 'Exit Channel', type: 'exitChannel' },
                { name: 'Enter Channel', type: 'enterChannel' }
            ];
        }

        setOperations(availableOperations);
    }, [selectedAlertType, selectedDrawingConfig.value?.type]);


    if (!coord || chartName !== contextMenuState.value?.chartName) return null
    const value = y2v(coord.y)

    const addValueAlert = (triggerType: string, operation: string) => {
        const selectedOp = operations.find(op => op.type === operation);
        const alertConfig = {
            chart: chartName,
            symbol,
            timeframe,
            enabled: true,
            triggerType,
            muted: false,
            general: false,
            arg1: {
                type: 'plot',
                name: 'price',
                refId: chartStore.mainPlotStore?.id!
            },
            arg2: {
                type: 'value',
                name: 'value',
                refId: 'value',
                value
            },
            op: selectedOp || {
                name: 'cross',
                type: 'cross'
            }
        };
        return createAlertConfig(alertConfig)
            .then((created) => {
                success({
                    title: 'Alert added',
                    message: `Alert added for ${symbol} at ${getFormatNumStr(value)}`
                })
                handleClose()
                alertConfigList.value = [created, ...alertConfigList.value]
            })
            .catch((e) => {
                error({
                    title: 'Failed to add alert',
                    message: e.message
                })
            })
    }

    const addDrawingAlert = (triggerType: string, operation: string) => {
        if (!selectedDrawingConfig.value?.id) return;

        const selectedOp = operations.find(op => op.type === operation);
        const alertConfig = {
            chart: chartName,
            symbol,
            timeframe,
            enabled: true,
            triggerType,
            muted: false,
            general: false,
            arg1: {
                type: 'plot',
                name: 'price',
                refId: chartStore.mainPlotStore?.id!
            },
            arg2: {
                type: 'drawing',
                name: selectedDrawingConfig.value.type,
                refId: selectedDrawingConfig.value.id.toString()
            },
            op: selectedOp || {
                name: 'cross',
                type: 'cross'
            }
        };
        return createAlertConfig(alertConfig)
            .then((created) => {
                success({
                    title: 'Alert added',
                    message: `Alert added for ${symbol} drawing ${selectedDrawingConfig.value?.type}`
                })
                handleClose()
                alertConfigList.value = [created, ...alertConfigList.value]
            })
            .catch((e) => {
                error({
                    title: 'Failed to add alert',
                    message: e.message
                })
            })
    }

    const handleClose = () => {
        contextMenuState.value = null
        setSelectedAlertType('')
        setSelectedTriggerType('')
        setSelectedOperation('')
    }


    return (
        <ContextMenuContainer
            ref={menuRef}
            key={`contextMenu-` + coord!.x + '-' + coord!.y}
            style={{
                position: 'absolute',
                top: getMenuPosition().y,
                left: getMenuPosition().x,
                ...(selectedAlertType && {
                    width: '350px',
                    minWidth: '350px',
                    maxWidth: '350px'
                })
            }}
            onClick={(e: React.MouseEvent) => e.stopPropagation()}
            onContextMenu={(e: React.MouseEvent) => e.preventDefault()}
        >
            <ContextMenuItem onClick={(e) => {
                e.stopPropagation();
                console.log('Value alert clicked');
                setSelectedAlertType('value');
            }}>
                <span>Add an alert 🔔 at {getFormatNumStr(value)}</span>
            </ContextMenuItem>
            {
                selectedDrawingConfig.value && (
                    <ContextMenuItem onClick={(e) => {
                        e.stopPropagation();
                        console.log('Drawing alert clicked for:', selectedDrawingConfig.value?.type);
                        setSelectedAlertType('drawing');
                    }}>
                        <span>Add an alert 🔔 for {selectedDrawingConfig.value.type}</span>
                    </ContextMenuItem>
                )
            }

            {selectedAlertType && (
                <>
                    <ContextMenuDivider />
                    <div style={{ display: 'flex' }}>
                        <div style={{ flex: 1, borderRight: '1px solid rgba(7, 81, 207, 0.2)' }}>
                            <div style={{ padding: '5px 10px', fontSize: '11px', color: 'rgba(7, 81, 207, 0.6)', fontWeight: 'bold' }}>
                                Trigger Type:
                            </div>
                            {triggerTypes.map((triggerType, index) => (
                                <React.Fragment key={triggerType.type}>
                                    <ContextMenuItem
                                        type="action"
                                        selected={selectedTriggerType === triggerType.type}
                                        onClick={() => setSelectedTriggerType(triggerType.type)}
                                    >
                                        <span style={{ flex: 1 }}>{triggerType.name}</span>
                                        {selectedTriggerType === triggerType.type && <span>✓</span>}
                                    </ContextMenuItem>
                                    {index < triggerTypes.length - 1 && (
                                        <div style={{
                                            height: '1px',
                                            backgroundColor: 'rgba(7, 81, 207, 0.1)',
                                            margin: '2px 10px'
                                        }} />
                                    )}
                                </React.Fragment>
                            ))}
                        </div>
                        <div style={{ flex: 1 }}>
                            <div style={{ padding: '5px 10px', fontSize: '11px', color: 'rgba(7, 81, 207, 0.6)', fontWeight: 'bold' }}>
                                Operation:
                            </div>
                            {operations.map((operation, index) => (
                                <React.Fragment key={operation.type}>
                                    <ContextMenuItem
                                        type="action"
                                        selected={selectedOperation === operation.type}
                                        onClick={() => setSelectedOperation(operation.type)}
                                    >
                                        <span style={{ flex: 1 }}>{operation.name}</span>
                                        {selectedOperation === operation.type && <span>✓</span>}
                                    </ContextMenuItem>
                                    {index < operations.length - 1 && (
                                        <div style={{
                                            height: '1px',
                                            backgroundColor: 'rgba(7, 81, 207, 0.1)',
                                            margin: '2px 10px'
                                        }} />
                                    )}
                                </React.Fragment>
                            ))}
                        </div>
                    </div>
                    {selectedTriggerType && selectedOperation && (
                        <>
                            <ContextMenuDivider />
                            <ContextMenuItem
                                type='action'
                                onClick={() => selectedAlertType === 'value' ? addValueAlert(selectedTriggerType, selectedOperation) : addDrawingAlert(selectedTriggerType, selectedOperation)}
                                style={{ backgroundColor: 'rgba(7, 81, 207, 0.1)' }}
                            >
                                <span>Create Alert</span>
                            </ContextMenuItem>
                        </>
                    )}
                </>
            )}

            <ContextMenuDivider />
            <ContextMenuItem type='action' onClick={(e) => {
                e.stopPropagation();
                props.onClose?.();
                handleClose()
            }}>
                <span style={{ color: 'rgba(207, 7, 7, 0.7)' }}>Cancel</span>
            </ContextMenuItem>
        </ContextMenuContainer>
    );
};
