import { useState, JS<PERSON> } from "react";
import { getFormatNumStr } from "../commons/util";
import { useAppContext, useDataContext, useNotificationContext } from "../contexts/contexts";
import {
    AlertListContainer,
    AlertListEmpty,
    AlertListItem,
    AlertDescription,
    AlertDrawingDescription,
    AlertDrawingContent,
    AlertIconWrapper,
    AlertGenericIcon
} from "../styles/alertStyles";
import { TAlertConfig } from "../types/alertTypes";
import { deleteAlertConfig } from "../commons/api";
import RayIcon from "../icons/ray.icon";
import PriceRangeIcon from "../icons/priceRange.icon";
import HorizontalRayChannelIcon from "../icons/horizontalRayChannel.icon";
import TimeRangeIcon from "../icons/timeRange.icon";
import ProgressiveTradeDist from "../icons/progressiveTradeDist.icon";
import RangeTradeDist from "../icons/rangeTradeDist.icon";


export const AlertList = () => {
    const [crossedAlerts, setCrossedAlerts] = useState<string[]>([]);
    const { alertConfigList } = useDataContext()
    const { symbol, timeframe } = useAppContext()
    const { success, error } = useNotificationContext()
    alertConfigList.use()

    // Get appropriate icon for drawing type
    const getDrawingIcon = (drawingName: string) => {
        const iconProps = { size: 24 };

        switch (drawingName?.toLowerCase()) {
            case 'ray':
                return <AlertIconWrapper><RayIcon {...iconProps} /></AlertIconWrapper>;
            case 'pricerange':
            case 'price range':
                return <AlertIconWrapper><PriceRangeIcon {...iconProps} /></AlertIconWrapper>;
            case 'horizontalraychannel':
            case 'horizontal ray channel':
                return <AlertIconWrapper><HorizontalRayChannelIcon {...iconProps} /></AlertIconWrapper>;
            case 'timerange':
            case 'time range':
                return <AlertIconWrapper><TimeRangeIcon {...iconProps} /></AlertIconWrapper>;
            case 'progressivetradedist':
            case 'progressive trade dist':
                return <AlertIconWrapper><ProgressiveTradeDist {...iconProps} /></AlertIconWrapper>;
            case 'rangetradedist':
            case 'range trade dist':
                return <AlertIconWrapper><RangeTradeDist {...iconProps} /></AlertIconWrapper>;
            default:
                // Return a generic drawing icon or the first letter
                return <AlertGenericIcon>{drawingName?.[0]?.toUpperCase() || 'D'}</AlertGenericIcon>;
        }
    };

    // Handle single-click on alert item for navigation
    const handleSingleClick = (alert: TAlertConfig) => {
        // Navigate to the alert's symbol and timeframe
        if (alert.symbol && alert.timeframe) {
            const newPath = `/${alert.symbol}-${alert.timeframe}`;
            window.history.pushState(null, '', newPath);

            // Trigger popstate event to update the app state
            window.dispatchEvent(new PopStateEvent('popstate'));
        }
    };

    // Handle double-click on alert item
    const handleDoubleClick = (alert: TAlertConfig) => {
        // Add to crossed list first for visual feedback
        setCrossedAlerts((prev) => [...prev, alert.id]);

        // After a short delay for the visual effect, delete the alert
        setTimeout(() => {
            // Show notification
            success({
                title: 'Alert deleted',
                message: `Alert deleted for ${alert.symbol?.replace('usdt', '').toUpperCase()}/${alert.timeframe}`
            });

            // Delete the alert and keep it crossed out until deletion is complete
            deleteAlertConfig(alert.id)
                .then(() => {
                    alertConfigList.value = alertConfigList.value.filter((a) => a.id !== alert.id);
                    setCrossedAlerts((prev) => prev.filter((id) => id !== alert.id));
                    success({
                        title: 'Alert deleted',
                        message: `Alert deleted for ${alert.symbol?.replace('usdt', '').toUpperCase()}/${alert.timeframe}`
                    })
                })
                .catch((error) => {
                    // If deletion fails, remove from crossed list
                    setCrossedAlerts((prev) => prev.filter((id) => id !== alert.id));

                    error({
                        title: 'Failed to delete alert',
                        message: error.message
                    })
                });
        }, 500);
    };

    // Check if alert is for current symbol and timeframe
    const isCurrentSymbolAndTimeframe = (alert: TAlertConfig) => {
        return alert.symbol === symbol && alert.timeframe === timeframe;
    };

    return (
        <AlertListContainer>
            {alertConfigList.value.length === 0 ? (
                <AlertListEmpty><span style={{ direction: 'ltr', display: 'inline-block' }}>No alerts yet</span></AlertListEmpty>
            ) : (
                alertConfigList.value.map((alert) => (
                    <AlertListItem
                        key={alert.id}
                        crossed={crossedAlerts.includes(alert.id)}
                        current={isCurrentSymbolAndTimeframe(alert)}
                        onClick={() => handleSingleClick(alert)}
                        onDoubleClick={() => handleDoubleClick(alert)}
                    >
                        <span style={{ direction: 'ltr', display: 'inline-block' }}>
                            <AlertDescriptionComponent alert={alert} getDrawingIcon={getDrawingIcon} />
                        </span>
                    </AlertListItem>
                ))
            )}
        </AlertListContainer>
    );
};

// Separate AlertDescription component
type AlertDescriptionProps = {
    alert: TAlertConfig;
    getDrawingIcon: (drawingName: string) => JSX.Element;
};

const AlertDescriptionComponent = ({ alert, getDrawingIcon }: AlertDescriptionProps): JSX.Element => {
    const opText = alert.op ? `${alert.op.name} ` : '';
    const symbolText = alert.symbol?.replace('usdt', '').toUpperCase();
    const timeframeText = alert.timeframe;
    const alertId = alert.id.toString().substring(0, 6);

    // For price alerts
    if (alert.arg2?.type === 'value') {
        return (
            <AlertDescription>
                {symbolText}-{timeframeText} • {opText} {getFormatNumStr(alert.arg2.value!)} #{alertId}
            </AlertDescription>
        );
    }
    // For drawing alerts
    else if (alert.arg2?.type === 'drawing') {
        return (
            <AlertDrawingDescription>
                <span>{symbolText}-{timeframeText}</span>
                <AlertDrawingContent>
                    • {opText}{getDrawingIcon(alert.arg2.name)} #{alertId}
                </AlertDrawingContent>
            </AlertDrawingDescription>
        );
    }
    // Generic fallback
    else {
        return (
            <AlertDescription>
                {symbolText}-{timeframeText} • {opText}Alert #{alertId}
            </AlertDescription>
        );
    }
};
