
export default function HorizontalRayChannelIcon(props: {
    color?: string
    size?: number
}) {
    const size = props.size ?? 23
    const circleRadius = 1.5
    const circleCenter = 4
    const lineStart = circleCenter + circleRadius
    const lineEnd = size
    return (
        <svg
            width={props.size ?? 23}
            height={props.size ?? 23}
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <line x1={lineStart} y1="8" x2={lineEnd} y2="8" stroke={props.color ?? 'black'} />
            <line x1={lineStart} y1="16" x2={lineEnd} y2="16" stroke={props.color ?? 'black'} />
            <circle cx={circleCenter} cy="8" r={circleRadius} stroke={props.color ?? 'black'} fill="none" />
            <circle cx={circleCenter} cy="16" r={circleRadius} stroke={props.color ?? 'black'} fill="none" />
        </svg>
    )
}