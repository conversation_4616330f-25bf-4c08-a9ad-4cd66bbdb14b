import { styled, keyframes } from '@stitches/react';
import { ALERT_COLOR, YAXIS_WIDTH } from '../commons/constants';


const fadeIn = keyframes({
    '0%': { opacity: 0 },
    '100%': { opacity: 1 }
});

export const AlertListContainer = styled('div', {
    position: 'absolute',
    top: '50%',
    right: `${YAXIS_WIDTH + 10}px`, // Position exactly at the edge of yAxis
    maxHeight: 'calc(100% - 150px)',
    overflowY: 'auto',
    backgroundColor: 'transparent',
    zIndex: 0,
    animation: `${fadeIn} 0.3s ease forwards`,
    borderRadius: '12px',
    padding: '4px', // Add padding to prevent border clipping

    // Hide scrollbar but keep functionality
    '&::-webkit-scrollbar': {
        display: 'none',
    },
    scrollbarWidth: 'none',
    msOverflowStyle: 'none',
});

export const AlertListItem = styled('div', {
    padding: '6px 8px',
    marginBottom: '6px',
    fontSize: '14px',
    color: 'rgb(7, 81, 207)',
    cursor: 'pointer',
    transition: 'all 0.2s ease',
    borderRadius: '4px',
    position: 'relative',
    textAlign: 'right', // End-align text
    direction: 'rtl', // Allow content to extend to the left

    variants: {
        crossed: {
            true: {
                '&::after': {
                    content: '""',
                    position: 'absolute',
                    top: '50%',
                    left: '0',
                    right: '0',
                    height: '2px',
                    backgroundColor: 'rgb(7, 81, 207)',
                    opacity: 0.7,
                    // Make it look like a pencil scratch
                    backgroundImage: 'linear-gradient(270deg, rgb(7, 81, 207) 70%, transparent 30%)', // Changed to 270deg for RTL
                    backgroundSize: '6px 1px',
                }
            }
        },
        current: {
            true: {
                color: ALERT_COLOR,
            }
        }
    }
});

export const AlertListEmpty = styled('div', {
    padding: '10px',
    fontSize: '14px',
    color: 'rgba(7, 81, 207, 0.7)',
    textAlign: 'right', // End-align empty message to match other elements
    fontStyle: 'italic',
});

export const AlertDescription = styled('span', {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: '32px', // Consistent height for all alerts
    width: '100%'
});

export const AlertDrawingDescription = styled('span', {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    gap: '6px',
    minHeight: '32px', // Same height as AlertDescription
    width: '100%'
});

export const AlertDrawingContent = styled('span', {
    display: 'flex',
    alignItems: 'center',
    gap: '4px'
});

export const AlertIconWrapper = styled('span', {
    display: 'inline-flex',
    alignItems: 'center',
    justifyContent: 'center'
});

export const AlertGenericIcon = styled('span', {
    display: 'inline-flex',
    alignItems: 'center',
    justifyContent: 'center',
    width: '24px',
    height: '24px',
    backgroundColor: 'rgb(7, 81, 207)',
    color: 'white',
    borderRadius: '2px',
    textAlign: 'center',
    fontSize: '12px',
    fontWeight: 'bold'
});
